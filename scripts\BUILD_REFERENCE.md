# LoniBeta Build Script Quick Reference

## Overview
The `build.ps1` script creates production-ready builds of the LoniBeta platform with comprehensive validation, security checks, and deployment preparation.

## Quick Start

### Standard Build
```powershell
.\scripts\build.ps1
```

### Custom Build
```powershell
.\scripts\build.ps1 -BuildDirectory "release" -Verbose
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `BuildDirectory` | String | "build" | Output directory for build artifacts |
| `SkipTests` | Switch | False | Skip code quality checks (not recommended) |
| `CleanBuild` | Switch | False | Remove existing build directory first |
| `Verbose` | Switch | False | Enable detailed logging output |

## Build Process Steps

1. **Prerequisites Validation** - Checks for bun, uv, Python
2. **Project Structure Validation** - Verifies required files exist
3. **Frontend Build** - Builds React app with Vite
4. **Backend Preparation** - Installs dependencies, runs quality checks
5. **Build Directory Creation** - Creates clean output directory
6. **Artifact Copying** - Copies all necessary files with exclusions
7. **Production Scripts** - Creates start scripts and configuration
8. **Deployment Guide** - Generates deployment documentation

## Output Structure

```
build/
├── backend/                    # Python FastAPI backend
│   ├── lonibeta/              # Application code
│   ├── pyproject.toml         # Dependencies
│   └── .env.production        # Environment template
├── frontend/                   # React build output
│   ├── index.html
│   ├── assets/
│   └── ...
├── scripts/                    # Deployment scripts
├── docs/                      # Documentation
├── start-production.ps1       # PowerShell start script
├── start-production.bat       # Windows batch script
├── DEPLOYMENT.md              # Deployment guide
└── README.md                  # Project documentation
```

## Error Handling

The script includes comprehensive error handling:
- **Prerequisite Validation**: Ensures all tools are available
- **Build Validation**: Verifies successful compilation
- **Output Validation**: Checks build artifacts exist
- **Cleanup on Failure**: Removes partial builds on error

## Logging

All operations are logged with timestamps:
- **Console Output**: Color-coded status messages
- **Log File**: Detailed log saved to `build.log`
- **Build Statistics**: Size, file count, timing information

## Security Features

- **Path Validation**: Prevents path traversal attacks
- **Safe File Operations**: Protected against accidental deletions
- **Input Sanitization**: All parameters are validated
- **Critical Path Protection**: Blocks system directory operations

## Common Use Cases

### Development Build
```powershell
# Quick build without quality checks
.\scripts\build.ps1 -SkipTests -CleanBuild
```

### Release Build
```powershell
# Full production build with validation
.\scripts\build.ps1 -BuildDirectory "release-v1.0" -Verbose
```

### CI/CD Pipeline
```powershell
# Automated build with error handling
try {
    .\scripts\build.ps1 -CleanBuild
    Write-Host "Build successful"
} catch {
    Write-Error "Build failed: $_"
    exit 1
}
```

## Troubleshooting

### Common Issues

**"Command not found" errors:**
```powershell
# Install missing tools
npm install -g bun
pip install uv
```

**Permission denied:**
```powershell
# Run as administrator or check file permissions
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**Build directory exists:**
```powershell
# Use clean build to remove existing directory
.\scripts\build.ps1 -CleanBuild
```

### Debug Mode
```powershell
# Enable verbose output for debugging
.\scripts\build.ps1 -Verbose
```

### Log Analysis
```powershell
# Check detailed logs
Get-Content build.log | Select-String "ERROR"
```

## Performance Tips

1. **Use SSD storage** for faster file operations
2. **Close unnecessary applications** to free up resources
3. **Use `-SkipTests`** for development builds (not production)
4. **Enable `-Verbose`** only when debugging

## Integration

### Git Hooks
```bash
# Pre-commit hook
#!/bin/sh
powershell -Command ".\scripts\build.ps1 -SkipTests"
```

### CI/CD Integration
```yaml
# GitHub Actions example
- name: Build LoniBeta
  run: |
    .\scripts\build.ps1 -CleanBuild
  shell: powershell
```

## Best Practices

1. **Always run full builds** for production deployments
2. **Use version-specific build directories** for releases
3. **Validate build output** before deployment
4. **Keep build logs** for troubleshooting
5. **Test deployment scripts** in staging environment

## Support

For issues with the build script:
1. Check the log file (`build.log`)
2. Run with `-Verbose` for detailed output
3. Verify all prerequisites are installed
4. Check file permissions and disk space
5. Review the deployment guide (`DEPLOYMENT.md`)
