<#
.SYNOPSIS
    Build LoniBeta for production deployment

.DESCRIPTION
    This script builds the LoniBeta AI-Powered System Automation Platform for production.
    It performs frontend build, backend preparation, code quality checks, and creates
    a deployment package with all necessary files.

.PARAMETER BuildDirectory
    The directory where the build artifacts will be created (default: "build")

.PARAMETER SkipTests
    Skip code quality checks and tests (not recommended for production)

.PARAMETER Verbose
    Enable verbose logging for debugging

.PARAMETER CleanBuild
    Perform a clean build by removing existing build artifacts

.EXAMPLE
    .\build.ps1
    Standard production build

.EXAMPLE
    .\build.ps1 -BuildDirectory "release" -Verbose
    Build to custom directory with verbose output

.EXAMPLE
    .\build.ps1 -CleanBuild -SkipTests
    Clean build without quality checks (development only)

.NOTES
    Author: LoniBeta Development Team
    Version: 1.0.0
    Requires: PowerShell 5.1+, bun, uv, Python 3.11+
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidateNotNullOrEmpty()]
    [string]$BuildDirectory = "build",

    [Parameter(Mandatory = $false)]
    [switch]$SkipTests,

    [Parameter(Mandatory = $false)]
    [switch]$CleanBuild,

    [Parameter(Mandatory = $false)]
    [switch]$Verbose
)

# Set strict mode for better error handling
Set-StrictMode -Version Latest

# Enable verbose output if requested
if ($Verbose) {
    $VerbosePreference = "Continue"
}

# Global variables for script configuration
$Script:ProjectRoot = $PSScriptRoot | Split-Path -Parent
$Script:FrontendDir = Join-Path $ProjectRoot "frontend"
$Script:BackendDir = Join-Path $ProjectRoot "backend"
$Script:BuildDir = Join-Path $ProjectRoot $BuildDirectory
$Script:LogFile = Join-Path $ProjectRoot "build.log"
$Script:StartTime = Get-Date

# Error action preference for consistent error handling
$ErrorActionPreference = "Stop"

#region Utility Functions

function Write-LogMessage {
    <#
    .SYNOPSIS
        Write a timestamped log message to console and log file
    #>
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet("Info", "Warning", "Error", "Success")]
        [string]$Level = "Info",

        [Parameter(Mandatory = $false)]
        [string]$Color = "White"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"

    # Write to console with color
    $colorMap = @{
        "Info"    = "White"
        "Warning" = "Yellow"
        "Error"   = "Red"
        "Success" = "Green"
    }

    $consoleColor = if ($Color -ne "White") { $Color } else { $colorMap[$Level] }
    Write-Host $logEntry -ForegroundColor $consoleColor

    # Write to log file
    try {
        Add-Content -Path $Script:LogFile -Value $logEntry -Encoding UTF8
    }
    catch {
        Write-Warning "Failed to write to log file: $_"
    }
}

function Test-CommandAvailable {
    <#
    .SYNOPSIS
        Test if a command is available in the current session
    #>
    param(
        [Parameter(Mandatory = $true)]
        [string]$CommandName
    )

    try {
        $null = Get-Command $CommandName -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

function Invoke-SafeCommand {
    <#
    .SYNOPSIS
        Execute a command with proper error handling and logging
    #>
    param(
        [Parameter(Mandatory = $true)]
        [string]$Command,

        [Parameter(Mandatory = $false)]
        [string[]]$Arguments = @(),

        [Parameter(Mandatory = $false)]
        [string]$WorkingDirectory = $PWD,

        [Parameter(Mandatory = $false)]
        [string]$SuccessMessage = "Command completed successfully",

        [Parameter(Mandatory = $false)]
        [string]$ErrorMessage = "Command failed"
    )

    $originalLocation = Get-Location

    try {
        if ($WorkingDirectory -ne $PWD) {
            Write-LogMessage "Changing to directory: $WorkingDirectory" -Level "Info"
            Set-Location $WorkingDirectory
        }

        Write-LogMessage "Executing: $Command $($Arguments -join ' ')" -Level "Info"

        if ($Arguments.Count -gt 0) {
            & $Command @Arguments
        }
        else {
            & $Command
        }

        if ($LASTEXITCODE -ne 0) {
            throw "$ErrorMessage (Exit code: $LASTEXITCODE)"
        }

        Write-LogMessage $SuccessMessage -Level "Success" -Color "Green"
    }
    catch {
        Write-LogMessage "$ErrorMessage`: $_" -Level "Error"
        throw
    }
    finally {
        Set-Location $originalLocation
    }
}

function Remove-DirectorySafely {
    <#
    .SYNOPSIS
        Safely remove a directory with proper error handling
    #>
    param(
        [Parameter(Mandatory = $true)]
        [string]$Path,

        [Parameter(Mandatory = $false)]
        [switch]$Force
    )

    if (-not (Test-Path $Path)) {
        Write-LogMessage "Directory does not exist: $Path" -Level "Info"
        return
    }

    try {
        Write-LogMessage "Removing directory: $Path" -Level "Info"

        # Ensure we're not trying to delete critical system directories
        $resolvedPath = Resolve-Path $Path -ErrorAction Stop
        $criticalPaths = @("C:\", "C:\Windows", "C:\Program Files", "/", "/usr", "/etc")

        foreach ($criticalPath in $criticalPaths) {
            if ($resolvedPath.Path -eq $criticalPath -or $resolvedPath.Path.StartsWith("$criticalPath\") -or $resolvedPath.Path.StartsWith("$criticalPath/")) {
                throw "Attempted to delete critical system directory: $resolvedPath"
            }
        }

        if ($Force) {
            Remove-Item $Path -Recurse -Force -ErrorAction Stop
        }
        else {
            Remove-Item $Path -Recurse -ErrorAction Stop
        }

        Write-LogMessage "Successfully removed directory: $Path" -Level "Success"
    }
    catch {
        Write-LogMessage "Failed to remove directory $Path`: $_" -Level "Error"
        throw
    }
}

function Copy-ItemSafely {
    <#
    .SYNOPSIS
        Safely copy items with proper error handling and validation
    #>
    param(
        [Parameter(Mandatory = $true)]
        [string]$Source,

        [Parameter(Mandatory = $true)]
        [string]$Destination,

        [Parameter(Mandatory = $false)]
        [string[]]$Exclude = @(),

        [Parameter(Mandatory = $false)]
        [switch]$Recurse
    )

    if (-not (Test-Path $Source)) {
        throw "Source path does not exist: $Source"
    }

    try {
        Write-LogMessage "Copying from '$Source' to '$Destination'" -Level "Info"

        # Ensure destination directory exists
        $destinationParent = Split-Path $Destination -Parent
        if ($destinationParent -and -not (Test-Path $destinationParent)) {
            New-Item -ItemType Directory -Path $destinationParent -Force | Out-Null
        }

        $copyParams = @{
            Path        = $Source
            Destination = $Destination
            ErrorAction = "Stop"
        }

        if ($Recurse) {
            $copyParams.Recurse = $true
        }

        if ($Exclude.Count -gt 0) {
            $copyParams.Exclude = $Exclude
        }

        Copy-Item @copyParams
        Write-LogMessage "Successfully copied to: $Destination" -Level "Success"
    }
    catch {
        Write-LogMessage "Failed to copy from '$Source' to '$Destination': $_" -Level "Error"
        throw
    }
}

#endregion

#region Validation Functions

function Test-Prerequisites {
    <#
    .SYNOPSIS
        Validate all required tools and dependencies are available
    #>
    Write-LogMessage "🔍 Validating prerequisites..." -Level "Info" -Color "Cyan"

    $requiredCommands = @(
        @{ Name = "bun"; Description = "Bun JavaScript runtime" },
        @{ Name = "uv"; Description = "UV Python package manager" },
        @{ Name = "python"; Description = "Python interpreter" }
    )

    $missingCommands = @()

    foreach ($cmd in $requiredCommands) {
        if (-not (Test-CommandAvailable $cmd.Name)) {
            $missingCommands += $cmd
            Write-LogMessage "❌ Missing required command: $($cmd.Name) ($($cmd.Description))" -Level "Error"
        }
        else {
            Write-LogMessage "✅ Found: $($cmd.Name)" -Level "Success"
        }
    }

    if ($missingCommands.Count -gt 0) {
        Write-LogMessage "Missing required dependencies. Please install:" -Level "Error"
        foreach ($cmd in $missingCommands) {
            Write-LogMessage "  - $($cmd.Name): $($cmd.Description)" -Level "Error"
        }
        throw "Prerequisites validation failed"
    }

    Write-LogMessage "✅ All prerequisites validated successfully" -Level "Success" -Color "Green"
}

function Test-ProjectStructure {
    <#
    .SYNOPSIS
        Validate the project structure is correct
    #>
    Write-LogMessage "🔍 Validating project structure..." -Level "Info" -Color "Cyan"

    $requiredPaths = @(
        @{ Path = $Script:FrontendDir; Description = "Frontend directory" },
        @{ Path = $Script:BackendDir; Description = "Backend directory" },
        @{ Path = (Join-Path $Script:FrontendDir "package.json"); Description = "Frontend package.json" },
        @{ Path = (Join-Path $Script:BackendDir "pyproject.toml"); Description = "Backend pyproject.toml" }
    )

    foreach ($item in $requiredPaths) {
        if (-not (Test-Path $item.Path)) {
            throw "Required path not found: $($item.Path) ($($item.Description))"
        }
        Write-LogMessage "✅ Found: $($item.Description)" -Level "Success"
    }

    Write-LogMessage "✅ Project structure validated successfully" -Level "Success" -Color "Green"
}

#endregion

#region Build Functions

function Invoke-FrontendBuild {
    <#
    .SYNOPSIS
        Build the React frontend for production
    #>
    Write-LogMessage "⚛️  Building frontend..." -Level "Info" -Color "Yellow"

    try {
        # Validate frontend directory and dependencies
        if (-not (Test-Path (Join-Path $Script:FrontendDir "node_modules"))) {
            Write-LogMessage "Installing frontend dependencies..." -Level "Info"
            Invoke-SafeCommand -Command "bun" -Arguments @("install") -WorkingDirectory $Script:FrontendDir -SuccessMessage "Frontend dependencies installed" -ErrorMessage "Failed to install frontend dependencies"
        }

        # Run production build
        Write-LogMessage "Running production build..." -Level "Info"
        Invoke-SafeCommand -Command "bun" -Arguments @("run", "build") -WorkingDirectory $Script:FrontendDir -SuccessMessage "Frontend build completed successfully" -ErrorMessage "Frontend build failed"

        # Validate build output
        $distPath = Join-Path $Script:FrontendDir "dist"
        if (-not (Test-Path $distPath)) {
            throw "Frontend build output not found at: $distPath"
        }

        $buildFiles = Get-ChildItem $distPath -Recurse -File
        if ($buildFiles.Count -eq 0) {
            throw "Frontend build output is empty"
        }

        Write-LogMessage "✅ Frontend build successful ($($buildFiles.Count) files generated)" -Level "Success" -Color "Green"
    }
    catch {
        Write-LogMessage "❌ Frontend build failed: $_" -Level "Error"
        throw
    }
}

function Invoke-BackendBuild {
    <#
    .SYNOPSIS
        Prepare the Python backend for production
    #>
    Write-LogMessage "🐍 Preparing backend..." -Level "Info" -Color "Yellow"

    try {
        # Install production dependencies
        Write-LogMessage "Installing production dependencies..." -Level "Info"
        Invoke-SafeCommand -Command "uv" -Arguments @("sync", "--no-dev") -WorkingDirectory $Script:BackendDir -SuccessMessage "Production dependencies installed" -ErrorMessage "Failed to install production dependencies"

        # Run code quality checks (unless skipped)
        if (-not $SkipTests) {
            Write-LogMessage "Running code quality checks..." -Level "Info"

            $qualityChecks = @(
                @{ Command = "black"; Args = @("lonibeta", "--check"); Description = "Code formatting check" },
                @{ Command = "isort"; Args = @("lonibeta", "--check-only"); Description = "Import sorting check" },
                @{ Command = "flake8"; Args = @("lonibeta"); Description = "Linting check" },
                @{ Command = "mypy"; Args = @("lonibeta"); Description = "Type checking" }
            )

            foreach ($check in $qualityChecks) {
                Write-LogMessage "Running $($check.Description)..." -Level "Info"
                try {
                    Invoke-SafeCommand -Command "uv" -Arguments (@("run", $check.Command) + $check.Args) -WorkingDirectory $Script:BackendDir -SuccessMessage "$($check.Description) passed" -ErrorMessage "$($check.Description) failed"
                }
                catch {
                    Write-LogMessage "❌ $($check.Description) failed: $_" -Level "Error"
                    throw "Code quality checks failed"
                }
            }

            Write-LogMessage "✅ All code quality checks passed" -Level "Success" -Color "Green"
        }
        else {
            Write-LogMessage "⚠️  Skipping code quality checks (not recommended for production)" -Level "Warning"
        }

        Write-LogMessage "✅ Backend preparation completed successfully" -Level "Success" -Color "Green"
    }
    catch {
        Write-LogMessage "❌ Backend preparation failed: $_" -Level "Error"
        throw
    }
}

function New-BuildDirectory {
    <#
    .SYNOPSIS
        Create and prepare the build directory
    #>
    Write-LogMessage "📦 Creating build package..." -Level "Info" -Color "Yellow"

    try {
        # Clean existing build directory if requested or if it exists
        if ($CleanBuild -or (Test-Path $Script:BuildDir)) {
            if (Test-Path $Script:BuildDir) {
                Write-LogMessage "Removing existing build directory..." -Level "Info"
                Remove-DirectorySafely -Path $Script:BuildDir -Force
            }
        }

        # Create new build directory
        Write-LogMessage "Creating build directory: $Script:BuildDir" -Level "Info"
        New-Item -ItemType Directory -Path $Script:BuildDir -Force | Out-Null

        if (-not (Test-Path $Script:BuildDir)) {
            throw "Failed to create build directory: $Script:BuildDir"
        }

        Write-LogMessage "✅ Build directory created successfully" -Level "Success" -Color "Green"
    }
    catch {
        Write-LogMessage "❌ Failed to create build directory: $_" -Level "Error"
        throw
    }
}

function Copy-BuildArtifacts {
    <#
    .SYNOPSIS
        Copy all necessary files to the build directory
    #>
    Write-LogMessage "📁 Copying build artifacts..." -Level "Info" -Color "Yellow"

    try {
        # Define copy operations with validation
        $copyOperations = @(
            @{
                Source      = $Script:BackendDir
                Destination = Join-Path $Script:BuildDir "backend"
                Exclude     = @("__pycache__", "*.pyc", ".pytest_cache", "htmlcov", ".coverage", "*.log", ".venv", "venv")
                Recurse     = $true
                Description = "Backend files"
            },
            @{
                Source      = Join-Path $Script:FrontendDir "dist"
                Destination = Join-Path $Script:BuildDir "frontend"
                Exclude     = @()
                Recurse     = $true
                Description = "Frontend build"
            },
            @{
                Source      = Join-Path $Script:ProjectRoot "scripts"
                Destination = Join-Path $Script:BuildDir "scripts"
                Exclude     = @("*.log")
                Recurse     = $true
                Description = "Scripts"
            },
            @{
                Source      = Join-Path $Script:ProjectRoot "README.md"
                Destination = Join-Path $Script:BuildDir "README.md"
                Exclude     = @()
                Recurse     = $false
                Description = "README file"
            }
        )

        # Optional documentation copy
        $docsPath = Join-Path $Script:ProjectRoot "docs"
        if (Test-Path $docsPath) {
            $copyOperations += @{
                Source      = $docsPath
                Destination = Join-Path $Script:BuildDir "docs"
                Exclude     = @("*.tmp", "*.log")
                Recurse     = $true
                Description = "Documentation"
            }
        }

        # Execute copy operations
        foreach ($operation in $copyOperations) {
            Write-LogMessage "Copying $($operation.Description)..." -Level "Info"

            if (-not (Test-Path $operation.Source)) {
                if ($operation.Description -eq "Documentation") {
                    Write-LogMessage "⚠️  Documentation directory not found, skipping..." -Level "Warning"
                    continue
                }
                else {
                    throw "Source not found: $($operation.Source)"
                }
            }

            Copy-ItemSafely -Source $operation.Source -Destination $operation.Destination -Exclude $operation.Exclude -Recurse:$operation.Recurse
        }

        Write-LogMessage "✅ All build artifacts copied successfully" -Level "Success" -Color "Green"
    }
    catch {
        Write-LogMessage "❌ Failed to copy build artifacts: $_" -Level "Error"
        throw
    }
}

#endregion

#region Production Scripts

function New-ProductionStartScript {
    <#
    .SYNOPSIS
        Create production start script with proper configuration
    #>
    Write-LogMessage "🚀 Creating production start script..." -Level "Info" -Color "Yellow"

    try {
        $startScriptContent = @"
<#
.SYNOPSIS
    LoniBeta Production Start Script

.DESCRIPTION
    Starts the LoniBeta production server with optimized settings

.PARAMETER Host
    Host address to bind to (default: 0.0.0.0)

.PARAMETER Port
    Port number to listen on (default: 8000)

.PARAMETER Workers
    Number of worker processes (default: auto-detect)

.EXAMPLE
    .\start-production.ps1
    Start with default settings

.EXAMPLE
    .\start-production.ps1 -Host "127.0.0.1" -Port 8080 -Workers 4
    Start with custom settings
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = `$false)]
    [string]`$Host = "0.0.0.0",

    [Parameter(Mandatory = `$false)]
    [int]`$Port = 8000,

    [Parameter(Mandatory = `$false)]
    [int]`$Workers = 0
)

# Set error handling
`$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting LoniBeta Production Server..." -ForegroundColor Green
Write-Host "Host: `$Host" -ForegroundColor Cyan
Write-Host "Port: `$Port" -ForegroundColor Cyan

# Auto-detect workers if not specified
if (`$Workers -eq 0) {
    `$Workers = [Math]::Max(1, [Environment]::ProcessorCount - 1)
    Write-Host "Workers: `$Workers (auto-detected)" -ForegroundColor Cyan
} else {
    Write-Host "Workers: `$Workers" -ForegroundColor Cyan
}

# Change to backend directory
Set-Location backend

# Validate uv is available
if (-not (Get-Command uv -ErrorAction SilentlyContinue)) {
    Write-Host "❌ uv not found. Please install uv package manager." -ForegroundColor Red
    exit 1
}

# Start the server
try {
    Write-Host "Starting server..." -ForegroundColor Green
    uv run uvicorn lonibeta.main:app --host `$Host --port `$Port --workers `$Workers --access-log --log-level info
}
catch {
    Write-Host "❌ Failed to start server: `$_" -ForegroundColor Red
    exit 1
}
"@

        $startScriptPath = Join-Path $Script:BuildDir "start-production.ps1"
        $startScriptContent | Out-File -FilePath $startScriptPath -Encoding UTF8 -Force

        # Create a simple batch file for Windows compatibility
        $batchContent = @"
@echo off
echo Starting LoniBeta Production Server...
powershell.exe -ExecutionPolicy Bypass -File "%~dp0start-production.ps1" %*
"@

        $batchPath = Join-Path $Script:BuildDir "start-production.bat"
        $batchContent | Out-File -FilePath $batchPath -Encoding ASCII -Force

        Write-LogMessage "✅ Production start scripts created successfully" -Level "Success" -Color "Green"
    }
    catch {
        Write-LogMessage "❌ Failed to create production start script: $_" -Level "Error"
        throw
    }
}

function New-DeploymentGuide {
    <#
    .SYNOPSIS
        Create deployment guide and configuration files
    #>
    Write-LogMessage "📋 Creating deployment guide..." -Level "Info" -Color "Yellow"

    try {
        $deploymentGuide = @"
# LoniBeta Production Deployment Guide

## Prerequisites

### System Requirements
- **Operating System**: Windows 10/11, Windows Server 2019+, or Linux
- **Python**: 3.11 or higher
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: Minimum 2GB free space
- **Network**: Internet connection for initial setup

### Required Software
1. **Python 3.11+**: Download from https://python.org
2. **uv Package Manager**: Install with: ``pip install uv``

## Deployment Steps

### 1. Copy Files
Copy the entire build directory to your production server.

### 2. Install Dependencies
```powershell
cd backend
uv sync --no-dev
```

### 3. Environment Configuration
Create a `.env` file in the backend directory with your production settings:
```
# Database Configuration
DATABASE_URL=sqlite:///./lonibeta.db

# Security Settings
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=your-domain.com,localhost

# AI Configuration
OLLAMA_BASE_URL=http://localhost:11434
HUGGINGFACE_API_KEY=your-hf-key-here

# Logging
LOG_LEVEL=INFO
LOG_FILE=lonibeta.log
```

### 4. Database Setup
```powershell
cd backend
uv run alembic upgrade head
```

### 5. Start the Server

#### Option A: PowerShell (Recommended)
```powershell
.\start-production.ps1
```

#### Option B: Batch File (Windows)
```cmd
start-production.bat
```

#### Option C: Manual Start
```powershell
cd backend
uv run uvicorn lonibeta.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 6. Verify Deployment
- Open browser to: http://your-server:8000
- Check API documentation: http://your-server:8000/docs
- Monitor logs for any errors

## Production Considerations

### Security
- Change default SECRET_KEY in .env file
- Configure firewall rules
- Use HTTPS in production (reverse proxy recommended)
- Regularly update dependencies

### Performance
- Adjust worker count based on CPU cores
- Configure database connection pooling
- Set up monitoring and logging
- Consider using a reverse proxy (nginx, Apache)

### Monitoring
- Check logs regularly: ``tail -f backend/lonibeta.log``
- Monitor system resources (CPU, memory, disk)
- Set up health checks and alerts

### Backup
- Backup database regularly
- Backup configuration files
- Test restore procedures

## Troubleshooting

### Common Issues
1. **Port already in use**: Change port in start script or stop conflicting service
2. **Permission denied**: Run as administrator or check file permissions
3. **Module not found**: Ensure all dependencies are installed with ``uv sync``
4. **Database errors**: Check database file permissions and disk space

### Getting Help
- Check logs in backend/lonibeta.log
- Review API documentation at /docs endpoint
- Verify all prerequisites are installed

## Build Information
- Build Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
- Build Directory: $Script:BuildDir
- PowerShell Version: $($PSVersionTable.PSVersion)
- Platform: $($PSVersionTable.Platform)
"@

        $guidePath = Join-Path $Script:BuildDir "DEPLOYMENT.md"
        $deploymentGuide | Out-File -FilePath $guidePath -Encoding UTF8 -Force

        # Create sample environment file
        $envSample = @"
# LoniBeta Production Environment Configuration
# Copy this file to .env and update with your settings

# Database Configuration
DATABASE_URL=sqlite:///./lonibeta.db

# Security Settings (CHANGE THESE IN PRODUCTION!)
SECRET_KEY=change-this-secret-key-in-production
ALLOWED_HOSTS=localhost,127.0.0.1

# AI Configuration
OLLAMA_BASE_URL=http://localhost:11434
HUGGINGFACE_API_KEY=

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=lonibeta.log

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=0

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_CODE_EXECUTION=true
ENABLE_MONITORING=true
"@

        $envPath = Join-Path $Script:BuildDir "backend" ".env.production"
        $envSample | Out-File -FilePath $envPath -Encoding UTF8 -Force

        Write-LogMessage "✅ Deployment guide and configuration files created" -Level "Success" -Color "Green"
    }
    catch {
        Write-LogMessage "❌ Failed to create deployment guide: $_" -Level "Error"
        throw
    }
}

#endregion

#region Main Execution

function Invoke-BuildProcess {
    <#
    .SYNOPSIS
        Main build process orchestration
    #>
    try {
        Write-LogMessage "🏗️  Starting LoniBeta Production Build..." -Level "Info" -Color "Green"
        Write-LogMessage "Build started at: $Script:StartTime" -Level "Info"
        Write-LogMessage "Build directory: $Script:BuildDir" -Level "Info"
        Write-LogMessage "Project root: $Script:ProjectRoot" -Level "Info"

        # Step 1: Validate prerequisites
        Test-Prerequisites

        # Step 2: Validate project structure
        Test-ProjectStructure

        # Step 3: Build frontend
        Invoke-FrontendBuild

        # Step 4: Build backend
        Invoke-BackendBuild

        # Step 5: Create build directory
        New-BuildDirectory

        # Step 6: Copy build artifacts
        Copy-BuildArtifacts

        # Step 7: Create production scripts
        New-ProductionStartScript

        # Step 8: Create deployment guide
        New-DeploymentGuide

        # Calculate build time
        $buildTime = (Get-Date) - $Script:StartTime
        $buildTimeFormatted = "{0:mm}m {0:ss}s" -f $buildTime

        Write-LogMessage "🎉 Production build completed successfully!" -Level "Success" -Color "Green"
        Write-LogMessage "Build time: $buildTimeFormatted" -Level "Info" -Color "Cyan"
        Write-LogMessage "Build location: $Script:BuildDir" -Level "Info" -Color "Cyan"

        # Display deployment instructions
        Write-Host ""
        Write-LogMessage "📋 Deployment Instructions:" -Level "Info" -Color "Yellow"
        Write-LogMessage "1. Copy the build directory to your production server" -Level "Info" -Color "Cyan"
        Write-LogMessage "2. Install Python 3.11+ and uv package manager" -Level "Info" -Color "Cyan"
        Write-LogMessage "3. Configure environment variables (see .env.production)" -Level "Info" -Color "Cyan"
        Write-LogMessage "4. Run: .\start-production.ps1" -Level "Info" -Color "Cyan"
        Write-LogMessage "5. Access the application at: http://your-server:8000" -Level "Info" -Color "Cyan"
        Write-Host ""
        Write-LogMessage "📖 For detailed instructions, see: DEPLOYMENT.md" -Level "Info" -Color "Yellow"

        return $true
    }
    catch {
        $buildTime = (Get-Date) - $Script:StartTime
        $buildTimeFormatted = "{0:mm}m {0:ss}s" -f $buildTime

        Write-LogMessage "❌ Build failed after $buildTimeFormatted" -Level "Error"
        Write-LogMessage "Error: $_" -Level "Error"
        Write-LogMessage "Check the log file for details: $Script:LogFile" -Level "Error"

        return $false
    }
}

function Show-BuildSummary {
    <#
    .SYNOPSIS
        Display build summary and statistics
    #>
    param(
        [Parameter(Mandatory = $true)]
        [bool]$BuildSuccess
    )

    Write-Host ""
    Write-Host "=" * 80 -ForegroundColor Gray
    Write-Host "BUILD SUMMARY" -ForegroundColor White
    Write-Host "=" * 80 -ForegroundColor Gray

    if ($BuildSuccess) {
        Write-Host "Status: SUCCESS" -ForegroundColor Green

        # Display build statistics
        if (Test-Path $Script:BuildDir) {
            $buildSize = (Get-ChildItem $Script:BuildDir -Recurse -File | Measure-Object -Property Length -Sum).Sum
            $buildSizeMB = [Math]::Round($buildSize / 1MB, 2)
            $fileCount = (Get-ChildItem $Script:BuildDir -Recurse -File).Count

            Write-Host "Build Size: $buildSizeMB MB" -ForegroundColor Cyan
            Write-Host "File Count: $fileCount files" -ForegroundColor Cyan
        }
    }
    else {
        Write-Host "Status: FAILED" -ForegroundColor Red
    }

    $totalTime = (Get-Date) - $Script:StartTime
    $totalTimeFormatted = "{0:mm}m {0:ss}s" -f $totalTime
    Write-Host "Total Time: $totalTimeFormatted" -ForegroundColor Cyan
    Write-Host "Log File: $Script:LogFile" -ForegroundColor Cyan
    Write-Host "=" * 80 -ForegroundColor Gray
}

#endregion

#region Script Entry Point

# Initialize logging
try {
    # Ensure log directory exists
    $logDir = Split-Path $Script:LogFile -Parent
    if (-not (Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }

    # Initialize log file
    "Build started at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" | Out-File -FilePath $Script:LogFile -Encoding UTF8 -Force
}
catch {
    Write-Warning "Failed to initialize logging: $_"
}

# Main execution
try {
    Write-LogMessage "Initializing build process..." -Level "Info"
    Write-LogMessage "Parameters: BuildDirectory='$BuildDirectory', SkipTests=$SkipTests, CleanBuild=$CleanBuild, Verbose=$Verbose" -Level "Info"

    $buildResult = Invoke-BuildProcess
    Show-BuildSummary -BuildSuccess $buildResult

    if ($buildResult) {
        exit 0
    }
    else {
        exit 1
    }
}
catch {
    Write-LogMessage "Critical error during build process: $_" -Level "Error"
    Show-BuildSummary -BuildSuccess $false
    exit 1
}
finally {
    # Cleanup and final logging
    Write-LogMessage "Build process completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -Level "Info"
}

#endregion
